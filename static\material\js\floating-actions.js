/**
 * Generic Floating Actions Component
 * Provides reusable floating action bar that appears when items are selected
 */
class FloatingActions {
    constructor(options = {}) {
        this.containerId = options.containerId || 'floating-actions-container';
        this.actions = options.actions || [];
        this.position = options.position || 'bottom-center'; // bottom-center, bottom-left, bottom-right
        this.mobilePosition = options.mobilePosition || 'bottom-full'; // bottom-full, bottom-center
        this.showAnimation = options.showAnimation !== false; // default true
        this.autoHide = options.autoHide !== false; // default true
        this.zIndex = options.zIndex || 999;
        
        // Callbacks
        this.onShow = options.onShow || null;
        this.onHide = options.onHide || null;
        this.onActionClick = options.onActionClick || null;
        
        // Internal state
        this.isVisible = false;
        this.selectedCount = 0;
        this.container = null;
        this.actionBar = null;
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.createActionBar();
        this.bindEvents();
    }
    
    createContainer() {
        // Remove existing container if it exists
        const existingContainer = document.getElementById(this.containerId);
        if (existingContainer) {
            existingContainer.remove();
        }
        
        // Create new container
        this.container = document.createElement('div');
        this.container.id = this.containerId;
        this.container.style.position = 'fixed';
        this.container.style.zIndex = this.zIndex;
        this.container.style.pointerEvents = 'none'; // Allow clicks to pass through when hidden
        
        document.body.appendChild(this.container);
    }
    
    createActionBar() {
        this.actionBar = document.createElement('div');
        this.actionBar.className = 'bulk-action-bar';
        this.actionBar.style.pointerEvents = 'auto'; // Enable clicks on the action bar itself
        
        // Create selected count element
        const countElement = document.createElement('div');
        countElement.className = 'bulk-selected-count';
        countElement.id = 'bulk-selected-count';
        this.actionBar.appendChild(countElement);
        
        // Create actions container
        const actionsContainer = document.createElement('div');
        actionsContainer.className = 'bulk-actions';
        actionsContainer.id = 'bulk-actions-container';
        this.actionBar.appendChild(actionsContainer);
        
        this.container.appendChild(this.actionBar);
        
        // Update actions
        this.updateActions();
    }
    
    updateActions() {
        const actionsContainer = document.getElementById('bulk-actions-container');
        if (!actionsContainer) return;
        
        // Clear existing actions
        actionsContainer.innerHTML = '';
        
        // Add new actions
        this.actions.forEach(action => {
            const button = document.createElement('button');
            button.className = `bulk-action-btn ${action.type || ''}`;
            button.setAttribute('data-action', action.id);
            button.title = action.tooltip || action.label;
            
            if (action.disabled) {
                button.disabled = true;
            }
            
            // Add icon if provided
            if (action.icon) {
                const icon = document.createElement('span');
                icon.className = 'material-icons';
                icon.textContent = action.icon;
                button.appendChild(icon);
            }
            
            // Add label if provided
            if (action.label) {
                const label = document.createElement('span');
                label.textContent = action.label;
                button.appendChild(label);
            }
            
            actionsContainer.appendChild(button);
        });
    }
    
    bindEvents() {
        // Handle action button clicks
        this.container.addEventListener('click', (e) => {
            const actionBtn = e.target.closest('.bulk-action-btn');
            if (actionBtn && !actionBtn.disabled) {
                const actionId = actionBtn.getAttribute('data-action');
                const action = this.actions.find(a => a.id === actionId);
                
                if (action && action.handler) {
                    action.handler(this.selectedCount, actionId);
                } else if (this.onActionClick) {
                    this.onActionClick(actionId, this.selectedCount);
                }
            }
        });
        
        // Handle escape key to hide
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible && this.autoHide) {
                this.hide();
            }
        });
    }
    
    show(selectedCount = 0) {
        if (selectedCount <= 0) {
            this.hide();
            return;
        }
        
        this.selectedCount = selectedCount;
        this.updateSelectedCount();
        
        if (!this.isVisible) {
            this.isVisible = true;
            this.container.style.pointerEvents = 'auto';
            
            if (this.showAnimation) {
                this.actionBar.classList.add('visible');
            } else {
                this.actionBar.style.opacity = '1';
                this.actionBar.style.visibility = 'visible';
                this.actionBar.style.transform = this.getVisibleTransform();
            }
            
            if (this.onShow) {
                this.onShow(selectedCount);
            }
        } else {
            // Just update the count if already visible
            this.updateSelectedCount();
        }
    }
    
    hide() {
        if (this.isVisible) {
            this.isVisible = false;
            this.selectedCount = 0;
            this.container.style.pointerEvents = 'none';
            
            if (this.showAnimation) {
                this.actionBar.classList.remove('visible');
            } else {
                this.actionBar.style.opacity = '0';
                this.actionBar.style.visibility = 'hidden';
                this.actionBar.style.transform = this.getHiddenTransform();
            }
            
            if (this.onHide) {
                this.onHide();
            }
        }
    }
    
    updateSelectedCount() {
        const countElement = document.getElementById('bulk-selected-count');
        if (countElement) {
            const text = this.selectedCount === 1 ? '1 selected' : `${this.selectedCount} selected`;
            countElement.textContent = text;
        }
    }
    
    getVisibleTransform() {
        switch (this.position) {
            case 'bottom-left':
                return 'translateY(0)';
            case 'bottom-right':
                return 'translateY(0)';
            case 'bottom-center':
            default:
                return 'translateX(-50%) translateY(0)';
        }
    }
    
    getHiddenTransform() {
        switch (this.position) {
            case 'bottom-left':
                return 'translateY(20px)';
            case 'bottom-right':
                return 'translateY(20px)';
            case 'bottom-center':
            default:
                return 'translateX(-50%) translateY(20px)';
        }
    }
    
    // Public API methods
    addAction(action) {
        this.actions.push(action);
        this.updateActions();
    }
    
    removeAction(actionId) {
        this.actions = this.actions.filter(a => a.id !== actionId);
        this.updateActions();
    }
    
    updateAction(actionId, updates) {
        const actionIndex = this.actions.findIndex(a => a.id === actionId);
        if (actionIndex !== -1) {
            this.actions[actionIndex] = { ...this.actions[actionIndex], ...updates };
            this.updateActions();
        }
    }
    
    setActions(actions) {
        this.actions = actions;
        this.updateActions();
    }
    
    enableAction(actionId) {
        this.updateAction(actionId, { disabled: false });
    }
    
    disableAction(actionId) {
        this.updateAction(actionId, { disabled: true });
    }
    
    getSelectedCount() {
        return this.selectedCount;
    }
    
    isShowing() {
        return this.isVisible;
    }
    
    // Destroy the component
    destroy() {
        if (this.container) {
            this.container.remove();
        }
        
        // Remove event listeners
        document.removeEventListener('keydown', this.handleEscapeKey);
        
        this.container = null;
        this.actionBar = null;
        this.isVisible = false;
        this.selectedCount = 0;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloatingActions;
} else {
    window.FloatingActions = FloatingActions;
}
